// Facebook : Syntrade
//@version=6
strategy(
  title = "首次测试策略", overlay=false, max_labels_count=500,
  // 实时性
  calc_on_order_fills = true,
  // 每次交易满仓
  default_qty_type = strategy.percent_of_equity,
  default_qty_value = 90,
  currency = currency.USDT,
  slippage = 6,
  // 手续费
  commission_type = strategy.commission.percent,
  commission_value = 0.075,
  use_bar_magnifier = true,
  fill_orders_on_standard_ohlc = true)

strategy.risk.allow_entry_in(strategy.direction.long)
// 参数设置
kPeriod = input.int(14, "K周期")
dPeriod = input.int(3, "D周期")
smoothK = input.int(3, "K平滑")
OverB = input.int(80, "超买线")
OverS = input.int(20, "超卖线")
// 计算随机指标
k = ta.sma(ta.stoch(close, high, low, kPeriod), smoothK)
d = ta.sma(k, dPeriod)

plot(k, title="%K", color=#2962FF)
plot(d, title="%D", color=#FF6D00)
h0 = hline(OverB, "上轨", color=#787B86)
hline(50, "中轨", color=color.new(#787B86, 50))
h1 = hline(OverS, "下轨", color=#787B86)
// 超买超卖条件
isOB = k > OverB
isOS = k < OverS
// 超买状态追踪
var bool inOB = false
var float highestHigh = na
var int highestBarIndex = na
checkSell = false
// 主逻辑开始
if isOB
    if not inOB
        // 新进入超买区
        inOB := true
        highestHigh := high
        highestBarIndex := bar_index
    else
        // 仍在超买区，记录最高点
        if high > highestHigh
            highestHigh := high
            highestBarIndex := bar_index
else
    if inOB
        // 离开超买区，标记最高点并重置
        label.new(highestBarIndex, OverB + 25, "H", style=label.style_label_down, color=color.green, textcolor=color.white, size=size.tiny)
        inOB := false
        highestHigh := na
        highestBarIndex := na
        checkSell := true
        // === 超卖状态追踪 ===
// 超卖状态追踪
var bool inOS = false
var float lowestLow = na
var int lowestBarIndex = na
checkBuy = false
if isOS
    // 新进入超卖区
    if not inOS
        inOS := true
        lowestLow := low
        lowestBarIndex := bar_index
    else
        // 仍在超卖区，记录最低点
        if low < lowestLow
            lowestLow := low
            lowestBarIndex := bar_index
else
    if inOS
        // 离开超卖区，标记最低点并重置
        label.new(lowestBarIndex, OverS - 25, "L", style=label.style_label_up, color=color.red, textcolor=color.white, size=size.tiny)
        inOS := false
        lowestLow := na
        lowestBarIndex := na
        checkBuy := true

// === 策略部分 ===
// 当离开超卖区时做多，离开超买区时做空
if checkBuy
    log.info("buy {0}", close)
    strategy.entry("Buy", strategy.long, comment="超卖做多")
if checkSell
    log.info("Sell {0}", close)
    strategy.close("Buy")
    // strategy.entry("Sell", strategy.short, comment="超买做空")
    // strategy.close_all()
// 结束