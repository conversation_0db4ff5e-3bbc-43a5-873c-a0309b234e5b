// ZigZag风格的H/L信号指标 - 配合main.pine使用
//@version=6
indicator("ZigZag H/L 信号指标", overlay=true, max_labels_count=500, max_lines_count=500)

// === 参数设置 ===
kPeriod = input.int(14, "K周期")
dPeriod = input.int(3, "D周期")
smoothK = input.int(3, "K平滑")
OverB = input.int(80, "超买线")
OverS = input.int(20, "超卖线")

// === 过滤参数 ===
minPriceChange = input.float(0.5, "最小价格变化百分比 (%)", minval=0.1, maxval=5.0, step=0.1)
confirmBars = input.int(2, "确认K线数", minval=1, maxval=10)
feeThreshold = input.float(0.4, "手续费阈值 (%)", minval=0.2, maxval=2.0, step=0.1)

// === 显示选项 ===
showZigZagLines = input.bool(true, "显示ZigZag连线")
showPriceLabels = input.bool(true, "显示价格标签")
showPercentChange = input.bool(true, "显示价格变化百分比")

// === 计算随机指标 ===
k = ta.sma(ta.stoch(close, high, low, kPeriod), smoothK)
d = ta.sma(k, dPeriod)

// === 超买超卖条件 ===
isOB = k > OverB
isOS = k < OverS

// === 价格变化计算函数 ===
priceChangePercent(price1, price2) =>
    math.abs(price1 - price2) / price2 * 100

// === 全局变量 ===
var float lastSignalPrice = na
var int lastSignalType = 0  // 0=无, 1=买入, -1=卖出
var int lastSignalBar = 0
var line lastZigZagLine = na

// === 超买状态追踪 ===
var bool inOB = false
var float highestHigh = na
var int highestBarIndex = na
var int obConfirmCount = 0

// === 超卖状态追踪 ===
var bool inOS = false
var float lowestLow = na
var int lowestBarIndex = na
var int osConfirmCount = 0

// === 主逻辑 - 超买区域 ===
if isOB
    if not inOB
        inOB := true
        highestHigh := high
        highestBarIndex := bar_index
        obConfirmCount := 0
    else
        if high > highestHigh
            highestHigh := high
            highestBarIndex := bar_index
else
    if inOB
        obConfirmCount += 1
        if obConfirmCount >= confirmBars
            priceChange = na(lastSignalPrice) ? feeThreshold + 1 : priceChangePercent(highestHigh, lastSignalPrice)
            
            if priceChange >= math.max(minPriceChange, feeThreshold) and lastSignalType != -1
                // 创建高点标签
                labelText = showPriceLabels ? "H\n" + str.tostring(highestHigh, "#.####") : "H"
                if showPercentChange and not na(lastSignalPrice)
                    labelText += "\n+" + str.tostring(priceChange, "#.##") + "%"
                
                label.new(highestBarIndex, highestHigh, labelText, 
                         style=label.style_label_down, color=color.red, 
                         textcolor=color.white, size=size.normal)
                
                // 绘制ZigZag线
                if showZigZagLines and not na(lastSignalPrice) and lastSignalBar > 0
                    line.new(lastSignalBar, lastSignalPrice, highestBarIndex, highestHigh, 
                            color=color.blue, width=2, style=line.style_solid)
                
                lastSignalPrice := highestHigh
                lastSignalType := -1
                lastSignalBar := highestBarIndex
        
        if obConfirmCount >= confirmBars
            inOB := false
            highestHigh := na
            highestBarIndex := na
            obConfirmCount := 0

// === 主逻辑 - 超卖区域 ===
if isOS
    if not inOS
        inOS := true
        lowestLow := low
        lowestBarIndex := bar_index
        osConfirmCount := 0
    else
        if low < lowestLow
            lowestLow := low
            lowestBarIndex := bar_index
else
    if inOS
        osConfirmCount += 1
        if osConfirmCount >= confirmBars
            priceChange = na(lastSignalPrice) ? feeThreshold + 1 : priceChangePercent(lastSignalPrice, lowestLow)
            
            if priceChange >= math.max(minPriceChange, feeThreshold) and lastSignalType != 1
                // 创建低点标签
                labelText = showPriceLabels ? "L\n" + str.tostring(lowestLow, "#.####") : "L"
                if showPercentChange and not na(lastSignalPrice)
                    labelText += "\n-" + str.tostring(priceChange, "#.##") + "%"
                
                label.new(lowestBarIndex, lowestLow, labelText, 
                         style=label.style_label_up, color=color.green, 
                         textcolor=color.white, size=size.normal)
                
                // 绘制ZigZag线
                if showZigZagLines and not na(lastSignalPrice) and lastSignalBar > 0
                    line.new(lastSignalBar, lastSignalPrice, lowestBarIndex, lowestLow, 
                            color=color.blue, width=2, style=line.style_solid)
                
                lastSignalPrice := lowestLow
                lastSignalType := 1
                lastSignalBar := lowestBarIndex
        
        if osConfirmCount >= confirmBars
            inOS := false
            lowestLow := na
            lowestBarIndex := na
            osConfirmCount := 0

// === 信息面板 ===
var table infoTable = table.new(position.top_left, 2, 7, bgcolor=color.new(color.white, 80), border_width=1)
if barstate.islast
    table.cell(infoTable, 0, 0, "ZigZag H/L 指标", text_color=color.black, bgcolor=color.gray, text_size=size.normal)
    table.cell(infoTable, 1, 0, "状态", text_color=color.black, bgcolor=color.gray, text_size=size.normal)
    table.cell(infoTable, 0, 1, "最小价格变化", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 1, str.tostring(minPriceChange) + "%", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 0, 2, "手续费阈值", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 2, str.tostring(feeThreshold) + "%", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 0, 3, "确认K线数", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 3, str.tostring(confirmBars), text_color=color.black, text_size=size.small)
    table.cell(infoTable, 0, 4, "当前K值", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 4, str.tostring(k, "#.##"), text_color=color.black, text_size=size.small)
    table.cell(infoTable, 0, 5, "上次信号价格", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 5, na(lastSignalPrice) ? "无" : str.tostring(lastSignalPrice, "#.####"), text_color=color.black, text_size=size.small)
    table.cell(infoTable, 0, 6, "上次信号类型", text_color=color.black, text_size=size.small)
    signalTypeText = lastSignalType == 1 ? "买入(L)" : lastSignalType == -1 ? "卖出(H)" : "无"
    signalColor = lastSignalType == 1 ? color.green : lastSignalType == -1 ? color.red : color.gray
    table.cell(infoTable, 1, 6, signalTypeText, text_color=signalColor, text_size=size.small)
